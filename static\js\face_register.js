document.addEventListener('DOMContentLoaded', async () => {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas'); // 用于绘制反馈和最终捕获
    const previewCanvas = document.getElementById('previewCanvas'); // 可选：用于显示捕获的图像
    const ctx = canvas.getContext('2d');
    const previewCtx = previewCanvas ? previewCanvas.getContext('2d') : null;
    const statusDiv = document.getElementById('status');
    const registerFormDiv = document.getElementById('register-form-div'); // 表单容器
    const registerForm = document.getElementById('register-form'); // 实际表单
    const nameInput = document.getElementById('name'); // 捕获后聚焦的输入框

    let capturedImageDataURL = null; // 存储捕获的图像数据URL
    let checkInterval = null; // 人脸质量检查定时器
    let consecutiveValidFrames = 0; // 有效帧连续计数器
    const requiredConsecutiveFrames = 5; // 需要连续有效帧数（例如5帧*200ms间隔=1秒）
    const checkIntervalMs = 1000; // 人脸质量检查间隔（从200ms调整为1000ms以降低服务器负载）
    let isCapturing = false; // 防止重复捕获标志

    // 1. 初始化摄像头
    async function initCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { width: 640, height: 480, facingMode: 'user' },
                audio: false
            });
            video.srcObject = stream;
            video.onloadedmetadata = () => video.play();
            return true;
        } catch (err) {
            statusDiv.textContent = `摄像头错误: ${err.message}`;
            statusDiv.className = 'error';
            return false;
        }
    }

    // 2. 自动拍照逻辑
    async function startAutoCapture() {
        if (checkInterval) clearInterval(checkInterval); 
        isCapturing = false;
        capturedImageDataURL = null;
        consecutiveValidFrames = 0;
        registerFormDiv.style.display = 'none'; // 初始隐藏表单
        if (previewCanvas) previewCtx.clearRect(0, 0, previewCanvas.width, previewCanvas.height); // 清除预览

        statusDiv.textContent = '请将正脸对准摄像头...';
        statusDiv.className = 'info';

        checkInterval = setInterval(async () => {
            if (isCapturing) return; // 如果正在捕获则不再检查

            // 将当前视频帧绘制到主画布（用于发送和反馈）
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // 获取Base64格式的图像数据
            const imageData = canvas.toDataURL('image/jpeg', 0.8); // Quality 0.8

            try {
                const response = await fetch('/check_face', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ image: imageData })
                });

                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.status}`);
                }

                const result = await response.json();
                statusDiv.textContent = result.message || '正在检测...'; // 更新状态信息

                // 清除旧绘制内容前先绘制新内容
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height); // 重绘视频帧

                if (result.valid) {
                    statusDiv.className = 'success';
                    consecutiveValidFrames++;
                    // 有效时绘制绿色框
                    if (result.face_area) {
                        const { x, y, w, h } = result.face_area;
                        ctx.strokeStyle = '#00FF00';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(x, y, w, h);
                    }
                } else {
                    statusDiv.className = 'warning'; // 使用警告类型显示指导信息
                    consecutiveValidFrames = 0; // 重置计数器（如果无效）
                    // 检测到人脸但无效时绘制红色框
                    if (result.face_area) {
                        const { x, y, w, h } = result.face_area;
                        ctx.strokeStyle = '#FF0000'; 
                        ctx.lineWidth = 2;
                        ctx.strokeRect(x, y, w, h);
                    }
                }

                // 检查我们是否有足够的连续有效帧
                if (consecutiveValidFrames >= requiredConsecutiveFrames) {
                    captureFace();
                }

            } catch (error) {
                console.error('人脸质量检查失败:', error);
                statusDiv.textContent = `检查失败: ${error.message}`;
                statusDiv.className = 'error';
                consecutiveValidFrames = 0; // 错误时重置帧数
            }

        }, checkIntervalMs);
    }

    function captureFace() {
        if (isCapturing) return;
        isCapturing = true; 
        clearInterval(checkInterval); // 停止检查
        checkInterval = null;

        // 从画布上捕获最终图像
        capturedImageDataURL = canvas.toDataURL('image/jpeg', 0.9); // 更高的最终捕获质量

        statusDiv.textContent = '拍照成功！请填写以下信息。';
        statusDiv.className = 'success';

        // 在预览画布中显示捕获的图像
        if (previewCanvas && previewCtx) {
            const img = new Image();
            img.onload = () => {
                previewCanvas.width = img.width;
                previewCanvas.height = img.height;
                previewCtx.drawImage(img, 0, 0);
            };
            img.src = capturedImageDataURL;
            previewCanvas.style.display = 'block'; // Show preview
        }

        // Show the registration form
        registerFormDiv.style.display = 'block';
        nameInput.focus(); // Focus on the first input field
    }


    // 3. 表单提交 (Modified to use capturedImageDataURL)
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!capturedImageDataURL) {
            statusDiv.textContent = '错误：尚未捕获照片。';
            statusDiv.className = 'error';
            return;
        }

        const formData = new FormData();
        formData.append('name', nameInput.value);
        formData.append('gender', document.getElementById('gender').value);
        formData.append('position', document.getElementById('position').value);

        // 将Base64数据URL转换为Blob
        const fetchRes = await fetch(capturedImageDataURL);
        const blob = await fetchRes.blob();
        formData.append('image', blob, 'face.jpg'); // 以名为“face.jpg”的文件发送

        statusDiv.textContent = '正在提交...';
        statusDiv.className = 'info';

        try {
            const response = await fetch('/api/register', {
                method: 'POST',
                body: formData
                // 发送FormData时不需要“Content-Type”标头，浏览器会设置它
            });

            const result = await response.json(); // 期待JSON响应

            if (!response.ok || !result.success) {
                 // 使用来自服务器响应的消息（如果可用）
                throw new Error(result.message || `服务器错误: ${response.status}`);
            }

            statusDiv.textContent = result.message || '注册成功！';
            statusDiv.className = 'success';
            // 延迟后可选择重定向
            setTimeout(() => {
                 window.location.href = '/';
            }, 2000);

        } catch (err) {
            console.error('提交失败:', err);
            statusDiv.textContent = `提交失败: ${err.message}`;
            statusDiv.className = 'error';
            // 允许用户再次尝试提交
        }
    });

    // 4. 初始化
    async function initialize() {
        if (!navigator.mediaDevices?.getUserMedia) {
            statusDiv.textContent = '错误：浏览器不支持摄像头访问 (getUserMedia)。';
            statusDiv.className = 'error';
            return;
        }
        const cameraReady = await initCamera();
        if (cameraReady) {
            video.onloadedmetadata = () => { // 等待视频尺寸准备就绪
                startAutoCapture(); // 开始自动捕获流程
            };
        } else {
             statusDiv.textContent = '错误：无法启动摄像头。请检查权限。';
             statusDiv.className = 'error';
        }
    }

    initialize(); // 页面加载时初始化

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        if (checkInterval) {
            clearInterval(checkInterval);
        }
        if (video.srcObject) {
            video.srcObject.getTracks().forEach(track => track.stop());
        }
    });
});
