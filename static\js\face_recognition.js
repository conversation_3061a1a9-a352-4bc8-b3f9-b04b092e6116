// 初始化变量
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const resultDiv = document.getElementById('result');
const ctx = canvas.getContext('2d');
let ws;
let recognitionInterval;

// 初始化摄像头
async function initCamera() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: 640,
                height: 480,
                facingMode: 'user'
            },
            audio: false
        });
        video.srcObject = stream;
        return true;
    } catch (err) {
        console.error('摄像头访问失败:', err);
        resultDiv.textContent = '无法访问摄像头，请确保已授予权限';
        return false;
    }
}

// 初始化WebSocket连接
function initWebSocket() {
    // 根据当前页面协议自动选择WebSocket协议
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    ws = new WebSocket(`${protocol}//${window.location.host}/ws`);



    ws.onopen = () => {
        startRecognition();
    };

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);


            // 如果当前未显示成功消息，请清除以前的图形/消息
            if (!window.isShowingSuccess) {
                 ctx.clearRect(0, 0, canvas.width, canvas.height);
                 ctx.drawImage(video, 0, 0, canvas.width, canvas.height); // 继续显示视频源
                 resultDiv.textContent = '';
                 resultDiv.className = '';
            }

            if (data.status === 'recognized' && !window.isShowingSuccess) {
                window.isShowingSuccess = true; // 标记以防止立即覆盖成功消息

                const coords = data.face_coords;
                const name = data.name;
                const gender = data.gender;
                const position = data.position;
                const timestamp = data.timestamp;

                // 绘制面部框
                ctx.strokeStyle = '#00FF00'; // 成功的绿框
                ctx.lineWidth = 3;
                ctx.strokeRect(coords.x, coords.y, coords.width, coords.height);

                // 显示个人信息
                ctx.fillStyle = '#00FF00';
                ctx.font = 'bold 20px Arial';
                ctx.fillText(name, coords.x, coords.y - 10);
                // Optional: Add gender/position if needed
                // ctx.font = '16px Arial';
                // ctx.fillText(`职务: ${position}`, coords.x, coords.y + coords.height + 20); // Below box

                // 显示结果文本
                let statusClass = '';
                let statusMessage = '';

                if (data.status === 'duplicate') {
                    statusClass = 'warning';
                    statusMessage = '重复打卡，请勿频繁操作';
                } else if (data.status === 'not_check_time') {
                    statusClass = 'error';
                    statusMessage = '非打卡时间，请在规定时间内打卡';
                } else if (data.status === 'late') {
                    statusClass = 'warning';
                    statusMessage = '迟到打卡成功';
                } else if (data.status === 'early_leave') {
                    statusClass = 'warning';
                    statusMessage = '早退打卡成功';
                } else if (data.status === 'abnormal') {
                    statusClass = 'warning';
                    statusMessage = '异常时间打卡成功';
                } else {
                    statusClass = 'success';
                    statusMessage = '打卡成功！';
                }


                // 创建全屏闪烁效果，只在非非打卡时间状态下显示
                if (data.status !== 'not_check_time') {
                    const flashOverlay = document.createElement('div');
                    flashOverlay.className = 'flash-overlay';
                    document.body.appendChild(flashOverlay);

                    // 800毫秒后移除闪烁效果
                    setTimeout(() => {
                        document.body.removeChild(flashOverlay);
                    }, 800);
                }

                // 非打卡时间的特殊处理
                if (data.status === 'not_check_time') {
                    resultDiv.innerHTML = `
                        <div class="result-box ${statusClass} fade-in-up">
                            <h2>非打卡时间</h2>
                            <h3>请在规定时间内打卡</h3>
                            <div class="result-details">
                                <p>姓名: <strong>${name}</strong></p>
                                <p>性别: ${gender}</p>
                                <p>职务: ${position}</p>
                                <p>时间: ${timestamp}</p>
                                <p class="time-notice">当前不在打卡时间范围内，请查看考勤规则或联系管理员</p>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result-box ${statusClass} fade-in-up">
                            <div class="success-badge">✓</div>
                            <h2>识别成功!</h2>
                            <h3>${statusMessage}</h3>
                            <div class="result-details">
                                <p>姓名: <strong>${name}</strong></p>
                                <p>性别: ${gender}</p>
                                <p>职务: ${position}</p>
                                <p>时间: ${timestamp}</p>
                                <p>类型: <strong>${data.check_type === 'check_in' ? '上班打卡' : data.check_type === 'check_out' ? '下班打卡' : '未知类型'}</strong></p>
                                <p>状态: <span class="${statusClass}">${data.status === 'normal' ? '正常' :
                                          data.status === 'late' ? '迟到' :
                                          data.status === 'early_leave' ? '早退' :
                                          data.status === 'abnormal' ? '异常' :
                                          data.status === 'duplicate' ? '重复打卡' :
                                          data.status === 'not_check_time' ? '非打卡时间' : '未知状态'}</span></p>
                            </div>
                        </div>
                    `;
                }
                resultDiv.className = 'recognized'; // Add class for styling

                // 5秒冷却延迟，给用户更多时间查看结果
                setTimeout(() => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height); // Restore video feed
                    resultDiv.innerHTML = '<p class="info-message">准备就绪，请面向摄像头...</p>';
                    resultDiv.className = 'info';
                    window.isShowingSuccess = false; // 重置标记
                }, 5000);

            } else if (data.status === 'not_recognized' && !window.isShowingSuccess) {
                resultDiv.textContent = '未匹配到人脸';
                resultDiv.className = 'info';

            } else if (data.status === 'error' && !window.isShowingSuccess) {
                resultDiv.textContent = `错误: ${data.message || '未知错误'}`;
                resultDiv.className = 'error';
            }
            // 如果window.isShowingSuccess为true，则忽略消息以避免闪烁

        } catch (e) {
            console.error('解析 WebSocket 消息失败:', e, "原始数据:", event.data);
            if (!window.isShowingSuccess) {
                 resultDiv.textContent = '通信错误';
                 resultDiv.className = 'error';
            }
        }
    };

    ws.onclose = () => {
        stopRecognition();
    };
}

// 开始人脸识别
function startRecognition() {
    if (recognitionInterval) clearInterval(recognitionInterval); // Clear existing interval if any

    recognitionInterval = setInterval(() => {
        if (ws.readyState !== WebSocket.OPEN || window.isShowingSuccess) {
             // 如果连接已关闭或当前显示成功消息，则不发送
             return;
        }

        // 设置画布大小以匹配视频固有大小，从而获得精确的坐标
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        //在画布上绘制视频帧（toBlob/toDataURL需要）
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 以JPEG Blob格式发送画布内容
        canvas.toBlob((blob) => {
            if (blob && ws.readyState === WebSocket.OPEN && !window.isShowingSuccess) {
                 ws.send(blob);
            }
        }, 'image/jpeg', 0.8); // Quality 0.8

    }, 300); // 300毫秒发送一次
}

// 停止人脸识别
function stopRecognition() {
    if (recognitionInterval) {
        clearInterval(recognitionInterval);
    }
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', async () => {
    window.isShowingSuccess = false; // 初始化标记
    const cameraSuccess = await initCamera();
    if (cameraSuccess) {
        // 等待视频准备就绪，以获得正确的尺寸
        video.onloadedmetadata = () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            initWebSocket(); // 仅在视频准备就绪后初始化WebSocket
        };
    }
});

// 页面关闭前清理资源
window.addEventListener('beforeunload', () => {
    stopRecognition();
    if (ws) {
        ws.close();
    }
    if (video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }
});
