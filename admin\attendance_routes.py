#!/usr/bin/env python3
import sqlite3
from datetime import datetime
from typing import Optional, List

from fastapi import APIRouter, Request, Depends, HTTPException, Form
from fastapi.responses import RedirectResponse
from fastapi.templating import Jinja2Templates

from admin.auth import get_current_admin_user
from admin.db_init import DATABASE

templates = Jinja2Templates(directory="templates")

router = APIRouter(prefix="/admin")

# 考勤规则管理
@router.get("/attendance-rules")
async def list_attendance_rules(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取所有考勤规则
    c.execute("""
        SELECT id, name, description, check_in_time, check_out_time,
               late_threshold, early_leave_threshold, work_days,
               location_required, location_range, latitude, longitude, is_default
        FROM attendance_rules
        ORDER BY is_default DESC, name
    """)
    rules = c.fetchall()

    # 获取所有班次
    c.execute("""
        SELECT id, name, start_time, end_time, description
        FROM shifts
        ORDER BY name
    """)
    shifts = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/attendance_rules.html", {
        "request": request,
        "current_tab": "attendance_rules",
        "rules": rules,
        "shifts": shifts,
        "logged_in_user": username
    })

@router.get("/attendance-rules/new")
async def new_attendance_rule_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    return templates.TemplateResponse("admin/attendance_rule_form.html", {
        "request": request,
        "current_tab": "attendance_rules",
        "rule": None,
        "logged_in_user": username
    })

@router.post("/attendance-rules")
async def create_attendance_rule(
    request: Request,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    description: str = Form(None),
    check_in_time: str = Form(...),
    check_out_time: str = Form(...),
    late_threshold: int = Form(15),
    early_leave_threshold: int = Form(15),
    work_days: List[str] = Form([]),
    location_required: Optional[str] = Form(None),
    latitude: Optional[float] = Form(None),
    longitude: Optional[float] = Form(None),
    location_range: int = Form(100),
    is_default: Optional[str] = Form(None)
):
    # 处理工作日
    work_days_str = ",".join(work_days)

    # 处理布尔值
    location_required_bool = location_required == "1"
    is_default_bool = is_default == "1"

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 如果设置为默认规则，先将其他规则设为非默认
        if is_default_bool:
            c.execute("UPDATE attendance_rules SET is_default = 0")

        # 创建考勤规则
        c.execute("""
            INSERT INTO attendance_rules (
                name, description, check_in_time, check_out_time,
                late_threshold, early_leave_threshold, work_days,
                location_required, location_range, latitude, longitude,
                is_default
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            name, description, check_in_time, check_out_time,
            late_threshold, early_leave_threshold, work_days_str,
            location_required_bool, location_range, latitude, longitude,
            is_default_bool
        ))

        conn.commit()
        return RedirectResponse(url="/admin/attendance-rules", status_code=303)
    except Exception as e:
        conn.rollback()

        return templates.TemplateResponse("admin/attendance_rule_form.html", {
            "request": request,
            "current_tab": "attendance_rules",
            "rule": {
                "name": name,
                "description": description,
                "check_in_time": check_in_time,
                "check_out_time": check_out_time,
                "late_threshold": late_threshold,
                "early_leave_threshold": early_leave_threshold,
                "work_days": work_days_str,
                "location_required": location_required_bool,
                "latitude": latitude,
                "longitude": longitude,
                "location_range": location_range,
                "is_default": is_default_bool
            },
            "error": f"创建考勤规则失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/attendance-rules/{rule_id}/edit")
async def edit_attendance_rule_form(
    request: Request,
    rule_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取考勤规则
    c.execute("""
        SELECT id, name, description, check_in_time, check_out_time,
               late_threshold, early_leave_threshold, work_days,
               location_required, location_range, latitude, longitude, is_default
        FROM attendance_rules
        WHERE id = ?
    """, (rule_id,))
    rule = c.fetchone()

    if not rule:
        conn.close()
        raise HTTPException(status_code=404, detail="考勤规则不存在")

    conn.close()

    return templates.TemplateResponse("admin/attendance_rule_form.html", {
        "request": request,
        "current_tab": "attendance_rules",
        "rule": rule,
        "logged_in_user": username
    })

@router.post("/attendance-rules/{rule_id}")
async def update_attendance_rule(
    request: Request,
    rule_id: int,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    description: str = Form(None),
    check_in_time: str = Form(...),
    check_out_time: str = Form(...),
    late_threshold: int = Form(15),
    early_leave_threshold: int = Form(15),
    work_days: List[str] = Form([]),
    location_required: Optional[str] = Form(None),
    latitude: Optional[float] = Form(None),
    longitude: Optional[float] = Form(None),
    location_range: int = Form(100),
    is_default: Optional[str] = Form(None)
):
    # 处理工作日
    work_days_str = ",".join(work_days)

    # 处理布尔值
    location_required_bool = location_required == "1"
    is_default_bool = is_default == "1"

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 如果设置为默认规则，先将其他规则设为非默认
        if is_default_bool:
            c.execute("UPDATE attendance_rules SET is_default = 0")

        # 更新考勤规则
        c.execute("""
            UPDATE attendance_rules SET
                name = ?, description = ?, check_in_time = ?, check_out_time = ?,
                late_threshold = ?, early_leave_threshold = ?, work_days = ?,
                location_required = ?, location_range = ?, latitude = ?, longitude = ?,
                is_default = ?
            WHERE id = ?
        """, (
            name, description, check_in_time, check_out_time,
            late_threshold, early_leave_threshold, work_days_str,
            location_required_bool, location_range, latitude, longitude,
            is_default_bool, rule_id
        ))

        conn.commit()
        return RedirectResponse(url="/admin/attendance-rules", status_code=303)
    except Exception as e:
        conn.rollback()

        return templates.TemplateResponse("admin/attendance_rule_form.html", {
            "request": request,
            "current_tab": "attendance_rules",
            "rule": {
                "id": rule_id,
                "name": name,
                "description": description,
                "check_in_time": check_in_time,
                "check_out_time": check_out_time,
                "late_threshold": late_threshold,
                "early_leave_threshold": early_leave_threshold,
                "work_days": work_days_str,
                "location_required": location_required_bool,
                "latitude": latitude,
                "longitude": longitude,
                "location_range": location_range,
                "is_default": is_default_bool
            },
            "error": f"更新考勤规则失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/attendance-rules/{rule_id}/delete")
async def delete_attendance_rule(
    rule_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查是否有人员关联此规则
        c.execute("SELECT COUNT(*) FROM person_attendance_rules WHERE rule_id = ?", (rule_id,))
        if c.fetchone()[0] > 0:
            conn.close()
            raise HTTPException(status_code=400, detail="无法删除，有人员关联此规则")

        # 检查是否为默认规则
        c.execute("SELECT is_default FROM attendance_rules WHERE id = ?", (rule_id,))
        result = c.fetchone()
        if not result:
            conn.close()
            raise HTTPException(status_code=404, detail="考勤规则不存在")

        is_default = result[0]
        if is_default:
            conn.close()
            raise HTTPException(status_code=400, detail="无法删除默认规则，请先设置其他规则为默认")

        # 删除考勤规则
        c.execute("DELETE FROM attendance_rules WHERE id = ?", (rule_id,))

        conn.commit()
        return RedirectResponse(url="/admin/attendance-rules", status_code=303)
    except HTTPException as e:
        raise e
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"删除考勤规则失败: {str(e)}")
    finally:
        conn.close()

# 测试删除考勤规则
@router.get("/attendance-rules/delete-test")
async def test_delete_rules(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取所有考勤规则
    c.execute("""
        SELECT id, name, check_in_time, check_out_time, is_default
        FROM attendance_rules
        ORDER BY is_default DESC, name
    """)
    rules = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/rule_delete_test.html", {
        "request": request,
        "rules": rules,
        "logged_in_user": username
    })

# 人员考勤规则管理
@router.get("/persons/{person_id}/rules")
async def person_rules(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取人员信息
    c.execute("""
        SELECT p.id, p.name, p.gender, p.position, p.employee_id,
               d.name as department_name
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE p.id = ?
    """, (person_id,))
    person = c.fetchone()

    if not person:
        conn.close()
        raise HTTPException(status_code=404, detail="人员不存在")

    # 获取人员的考勤规则
    c.execute("""
        SELECT pr.id, pr.effective_date, pr.expiry_date,
               ar.id as rule_id, ar.name as rule_name,
               ar.check_in_time, ar.check_out_time
        FROM person_attendance_rules pr
        JOIN attendance_rules ar ON pr.rule_id = ar.id
        WHERE pr.person_id = ?
        ORDER BY pr.effective_date DESC
    """, (person_id,))
    person_rules = c.fetchall()

    # 获取所有考勤规则
    c.execute("""
        SELECT id, name, check_in_time, check_out_time
        FROM attendance_rules
        ORDER BY is_default DESC, name
    """)
    all_rules = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/person_rules.html", {
        "request": request,
        "current_tab": "persons",
        "person": person,
        "person_rules": person_rules,
        "all_rules": all_rules,
        "now": datetime.now,
        "logged_in_user": username
    })

@router.post("/persons/{person_id}/rules")
async def add_person_rule(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user),
    rule_id: int = Form(...),
    effective_date: str = Form(...),
    expiry_date: Optional[str] = Form(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查人员是否存在
        c.execute("SELECT id FROM persons WHERE id = ?", (person_id,))
        if not c.fetchone():
            conn.close()
            raise HTTPException(status_code=404, detail="人员不存在")

        # 检查规则是否存在
        c.execute("SELECT id FROM attendance_rules WHERE id = ?", (rule_id,))
        if not c.fetchone():
            conn.close()
            raise HTTPException(status_code=404, detail="考勤规则不存在")

        # 添加人员考勤规则关联
        c.execute("""
            INSERT INTO person_attendance_rules (
                person_id, rule_id, effective_date, expiry_date
            ) VALUES (?, ?, ?, ?)
        """, (person_id, rule_id, effective_date, expiry_date))

        conn.commit()
        return RedirectResponse(url=f"/admin/persons/{person_id}/rules", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"添加考勤规则失败: {str(e)}")
    finally:
        conn.close()

@router.delete("/persons/{person_id}/rules/{association_id}")
async def delete_person_rule(
    request: Request,
    person_id: int,
    association_id: int,  # 改为使用关联记录ID
    username: str = Depends(get_current_admin_user)
):
    """删除人员考勤规则关联"""
    print(f"Deleting rule association: person_id={person_id}, association_id={association_id}")

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查记录是否存在
        c.execute("SELECT id FROM person_attendance_rules WHERE id = ?", (association_id,))
        record = c.fetchone()
        if not record:
            print(f"Record not found: association_id={association_id}")
            conn.close()
            # 返回错误页面而不是抛出异常
            return templates.TemplateResponse("admin/error.html", {
                "request": request,
                "error": "考勤规则不存在",
                "logged_in_user": username
            })

        # 删除人员考勤规则关联
        c.execute("DELETE FROM person_attendance_rules WHERE id = ?", (association_id,))
        rows_affected = c.rowcount
        print(f"Rows affected: {rows_affected}")

        conn.commit()
        return RedirectResponse(url=f"/admin/persons/{person_id}/rules", status_code=303)
    except Exception as e:
        conn.rollback()
        print(f"Error deleting rule: {str(e)}")
        # 返回错误页面而不是抛出异常
        return templates.TemplateResponse("admin/error.html", {
            "request": request,
            "error": f"删除考勤规则失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()
