#!/usr/bin/env python3
import os
import uuid
import sqlite3
import json
import base64
import datetime

import cv2
import numpy as np
from fastapi import FastAPI, Request, WebSocket, Depends, HTTPException, UploadFile, Form, File
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import RedirectResponse
from starlette.middleware.sessions import SessionMiddleware
from deepface import DeepFace

# 导入管理后台模块
from admin.db_init import DATABASE, init_all_db, verify_password
from admin import router as admin_router
from admin import attendance_router
from admin.user_person_routes import router as user_person_router
from admin.leave_routes import router as admin_leave_router
from admin.settings_manager import is_time_check_disabled

# 导入用户模块
from user import user_router
from user.leave_routes import router as user_leave_router

# --- 配置 ---
KNOWN_FACES_DIR = 'known_faces'
TEMP_DIR = 'temp_files'
FACE_DETECTION_BACKEND = "opencv"
FACE_RECOGNITION_MODEL = "VGG-Face"
FACE_VERIFICATION_THRESHOLD = 0.6

# 确保文件夹存在
os.makedirs(KNOWN_FACES_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)

# FastAPI 应用初始化
app = FastAPI()
security = HTTPBasic()

# 添加会话中间件
app.add_middleware(SessionMiddleware, secret_key="your-secret-key-for-face-recognition-system")

# 挂载文件
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/known_faces", StaticFiles(directory=KNOWN_FACES_DIR), name="known_faces")
templates = Jinja2Templates(directory="templates")

# --- 用户认证 ---
async def authenticate_user(credentials: HTTPBasicCredentials = Depends(security)):
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    c.execute("SELECT password_hash FROM users WHERE username=?", (credentials.username,))
    result = c.fetchone()
    conn.close()

    if not result or not verify_password(credentials.password, result[0]):
        raise HTTPException(
            status_code=401,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# 初始化数据库
init_all_db()

# 包含管理员和用户路由器
app.include_router(admin_router)
app.include_router(attendance_router)
app.include_router(user_person_router)
app.include_router(admin_leave_router)

# 添加用户路由器
@app.get("/user")
async def redirect_to_user(request: Request):
    # 从 URL 参数中获取用户名和凭据，并传递给 /user/
    username = request.query_params.get("username")
    token = request.query_params.get("token")

    if username and token:
        return RedirectResponse(url=f"/user/?username={username}&token={token}", status_code=307)  # 307保留原始请求方法和数据
    else:
        return RedirectResponse(url="/user/", status_code=307)  # 307保留原始请求方法和数据

app.include_router(user_router)
app.include_router(user_leave_router)



# --- API接口 ---
@app.get("/")
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/login")
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT id, password_hash, role FROM users WHERE username=?", (username,))
        result = c.fetchone()

        if not result or not verify_password(password, result[1]):
            return templates.TemplateResponse(
                "login.html",
                {"request": request, "error": "用户名或密码错误"}
            )

        # 设置会话
        request.session["username"] = username
        request.session["role"] = result[2]

        # 根据角色重定向
        if result[2] == "admin" or result[2] == "manager":
            return RedirectResponse(url="/admin/", status_code=303)
        else:
            return RedirectResponse(url="/user/", status_code=303)
    finally:
        conn.close()

@app.get("/logout")
async def logout(request: Request):
    # 清除会话
    request.session.clear()
    return RedirectResponse(url="/login", status_code=303)

@app.post("/auth_check")
async def auth_check(username: str = Depends(authenticate_user)):
    return {"message": "认证成功", "username": username}

@app.get("/api/user/role")
async def get_user_role(username: str = Depends(authenticate_user)):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT role FROM users WHERE username=?", (username,))
        result = c.fetchone()
        role = result[0] if result else "user"
        print(f"User role API: username={username}, role={role}")
        return {"username": username, "role": role}
    finally:
        conn.close()

@app.get("/debug/user-role")
async def debug_user_role():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, username, role FROM users")
    users = c.fetchall()
    conn.close()
    return {"users": [dict(user) for user in users]}

@app.get("/register")
async def register_page(request: Request, username: str = Depends(authenticate_user)):
    return templates.TemplateResponse("register.html", {"request": request, "username": username})

@app.post("/check_face")
async def check_face_quality(request: Request):
    try:
        data = await request.json()
        image_data_uri = data.get('image')
        if not image_data_uri or ',' not in image_data_uri:
            raise HTTPException(status_code=400, detail="无效的图像数据格式")

        image_b64 = image_data_uri.split(',')[1]
        image_bytes = base64.b64decode(image_b64)
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if img is None:
            raise HTTPException(status_code=400, detail="无法解码图像")

        img_height, img_width, _ = img.shape

        faces = DeepFace.extract_faces(
            img_path=img,
            enforce_detection=False,
            detector_backend=FACE_DETECTION_BACKEND
        )

        if not faces:
            return {"valid": False, "message": "未检测到人脸", "face_area": None}

        face = max(faces, key=lambda f: f['facial_area']['w'] * f['facial_area']['h'])
        facial_area = face['facial_area']
        confidence = face['confidence']

        face_ratio = (facial_area['w'] * facial_area['h']) / (img_width * img_height)
        size_ok = 0.05 < face_ratio < 0.8

        center_x_ok = abs(facial_area['x'] + facial_area['w']/2 - img_width/2) < img_width * 0.25
        center_y_ok = abs(facial_area['y'] + facial_area['h']/2 - img_height/2) < img_height * 0.25
        centering_ok = center_x_ok and center_y_ok

        confidence_ok = confidence > 0.90

        is_valid = size_ok and centering_ok and confidence_ok
        message = "请将人脸置于画面中央"
        if is_valid:
            message = "人脸质量良好"
        elif not confidence_ok:
            message = "人脸不够清晰"
        elif not size_ok:
            message = "请靠近或远离摄像头"
        elif not centering_ok:
            message = "请将人脸置于画面中央"

        return {
            "valid": bool(is_valid),
            "message": message,
            "face_area": {
                "x": int(facial_area['x']),
                "y": int(facial_area['y']),
                "w": int(facial_area['w']),
                "h": int(facial_area['h'])
            } if facial_area else None
        }

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        return {"valid": False, "message": f"检测时发生错误: {type(e).__name__}", "face_area": None}

@app.post("/api/register")
async def register_face_api(
    image: UploadFile = File(...),
    name: str = Form(...),
    gender: str = Form(...),
    position: str = Form(...)
):
    if not all([name, gender, position]):
        raise HTTPException(status_code=400, detail="请填写所有信息（姓名、性别、职位）")

    temp_image_path = None
    db_conn = None
    try:
        contents = await image.read()
        if not contents:
            raise HTTPException(status_code=400, detail="上传的图片为空")

        temp_suffix = os.path.splitext(image.filename)[1] or ".jpg"
        temp_image_path = os.path.join(TEMP_DIR, f"reg_{uuid.uuid4()}{temp_suffix}")
        with open(temp_image_path, "wb") as f:
            f.write(contents)

        try:
            faces = DeepFace.extract_faces(
                img_path=temp_image_path,
                enforce_detection=True,
                detector_backend=FACE_DETECTION_BACKEND
            )
            if not faces:
                raise ValueError("未能从上传的图片中检测到人脸")
        except Exception as face_err:
            raise HTTPException(status_code=400, detail=f"人脸检测失败: {face_err}")

        permanent_filename = f"{uuid.uuid4()}{temp_suffix}"
        permanent_filepath = os.path.join(KNOWN_FACES_DIR, permanent_filename)
        permanent_filepath_relative = permanent_filepath.replace("\\", "/")

        os.rename(temp_image_path, permanent_filepath)
        temp_image_path = None

        db_conn = sqlite3.connect(DATABASE)
        c = db_conn.cursor()
        try:
            c.execute("""
                INSERT INTO persons (name, gender, position, face_image_path)
                VALUES (?, ?, ?, ?)
            """, (name, gender, position, permanent_filepath_relative))
            db_conn.commit()
            return {"success": True, "message": f"用户 {name} 注册成功"}
        except sqlite3.IntegrityError:
            db_conn.rollback()
            os.remove(permanent_filepath)
            raise HTTPException(status_code=409, detail="人脸数据已存在或路径冲突")
        finally:
            if db_conn:
                db_conn.close()

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册过程中发生内部错误: {type(e).__name__}")
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
            except OSError:
                pass

@app.websocket("/ws")
async def websocket_recognition_endpoint(websocket: WebSocket):
    await websocket.accept()
    db_conn = None

    try:
        while True:
            data = await websocket.receive_bytes()
            if not data:
                continue

            nparr = np.frombuffer(data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if img is None:
                await websocket.send_text(json.dumps({"status": "error", "message": "无法解码图像"}))
                continue

            # 检测图像中的人脸
            face_detector = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            faces = face_detector.detectMultiScale(gray, 1.3, 5)

            print(f"检测到 {len(faces)} 个人脸")

            try:
                dfs = DeepFace.find(
                    img_path=img,
                    db_path=KNOWN_FACES_DIR,
                    model_name=FACE_RECOGNITION_MODEL,
                    enforce_detection=False,
                    detector_backend=FACE_DETECTION_BACKEND,
                    distance_metric="cosine",
                    silent=True
                )

                best_match_info = None

                if dfs and not dfs[0].empty:
                    df = dfs[0]
                    # 使用 'distance' 列进行过滤
                    distance_col = "distance"

                    # 打印列名以便调试
                    print(f"DataFrame columns: {df.columns.tolist()}")
                    print(f"Looking for column: {distance_col}")

                    # 检查列是否存在
                    if distance_col in df.columns:
                        df_filtered = df[df[distance_col] < FACE_VERIFICATION_THRESHOLD]
                        if not df_filtered.empty:
                            best_match = df_filtered.loc[df_filtered[distance_col].idxmin()]

                            # 只有当找到匹配时才处理
                            matched_image_path = best_match['identity'].replace("\\", "/")

                            db_conn = sqlite3.connect(DATABASE)
                            c = db_conn.cursor()
                            c.execute("""
                                SELECT id, name, gender, position
                                FROM persons
                                WHERE face_image_path = ?
                            """, (matched_image_path,))
                            person_data = c.fetchone()

                            if person_data:
                                person_id, name, gender, position = person_data
                                face_coords = {
                                    "x": int(best_match['source_x']),
                                    "y": int(best_match['source_y']),
                                    "width": int(best_match['source_w']),
                                    "height": int(best_match['source_h'])
                                }

                                best_match_info = {
                                    "status": "recognized",
                                    "person_id": person_id,
                                    "name": name,
                                    "gender": gender,
                                    "position": position,
                                    "face_coords": face_coords,
                                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                }

                                # 定义默认值，确保变量始终被定义
                                check_type = "unknown"
                                status = "normal"
                                should_insert_record = True

                                try:
                                    # 检查是否在短时间内重复打卡（30分钟内）
                                    c.execute("""
                                        SELECT id, timestamp, check_type
                                        FROM attendance
                                        WHERE person_id = ?
                                        AND datetime(timestamp) > datetime('now', 'localtime', '-30 minutes')
                                        ORDER BY timestamp DESC
                                        LIMIT 1
                                    """, (person_id,))
                                    recent_record = c.fetchone()

                                    # 获取该人员的考勤规则
                                    c.execute("""
                                        SELECT ar.check_in_time, ar.check_out_time, ar.late_threshold, ar.early_leave_threshold,
                                               ar.work_days, ar.location_required, ar.location_range
                                        FROM attendance_rules ar
                                        JOIN person_attendance_rules par ON ar.id = par.rule_id
                                        WHERE par.person_id = ? AND
                                              (par.expiry_date IS NULL OR date(par.expiry_date) >= date('now', 'localtime'))
                                        ORDER BY par.effective_date DESC
                                        LIMIT 1
                                    """, (person_id,))

                                    # 打印调试信息
                                    print(f"查询人员ID {person_id} 的考勤规则")
                                    rule = c.fetchone()

                                    # 定义默认值
                                    check_in_time = "09:00"
                                    check_out_time = "18:00"
                                    late_threshold = 15
                                    early_leave_threshold = 15

                                    # 如果没有找到个人规则，使用默认规则
                                    if not rule:
                                        print(f"未找到人员ID {person_id} 的个人考勤规则，尝试使用默认规则")
                                        c.execute("""
                                            SELECT check_in_time, check_out_time, late_threshold, early_leave_threshold,
                                                   work_days, location_required, location_range
                                            FROM attendance_rules
                                            WHERE is_default = 1
                                            LIMIT 1
                                        """)
                                        rule = c.fetchone()
                                        if rule:
                                            print("找到默认考勤规则")
                                        else:
                                            print("未找到默认考勤规则")

                                    # 如果找到了规则，使用规则中的值
                                    if rule:
                                        check_in_time = rule[0]
                                        check_out_time = rule[1]
                                        late_threshold = rule[2]
                                        early_leave_threshold = rule[3]

                                    # 获取当前时间
                                    now = datetime.datetime.now()
                                    current_time = now.strftime("%H:%M")

                                    # 将时间字符串转换为分钟数，便于比较
                                    def time_to_minutes(time_str):
                                        h, m = map(int, time_str.split(':'))
                                        return h * 60 + m

                                    current_minutes = time_to_minutes(current_time)
                                    check_in_minutes = time_to_minutes(check_in_time)
                                    check_out_minutes = time_to_minutes(check_out_time)

                                    # 获取当前星期（0-6，0为周日）
                                    current_weekday = now.weekday() + 1  # 转换为1-7，周一为1
                                    if current_weekday == 7:  # 如果是周日，转换为0
                                        current_weekday = 0

                                    # 检查是否为工作日
                                    work_days = rule[4].split(',') if rule and len(rule) > 4 and rule[4] else ["1", "2", "3", "4", "5"]
                                    work_days = [int(day) for day in work_days]
                                    is_work_day = current_weekday in work_days

                                    # 检查是否禁用了考勤时间检测
                                    time_check_disabled = is_time_check_disabled()

                                    if time_check_disabled:
                                        # 如果禁用了时间检测，允许任何时间打卡
                                        # 根据当前时间判断是上班还是下班打卡
                                        if current_minutes < check_in_minutes + (check_out_minutes - check_in_minutes) / 2:
                                            # 如果当前时间更接近上班时间，则为上班打卡
                                            check_type = "check_in"
                                            if current_minutes > check_in_minutes:
                                                status = "late"
                                        else:
                                            # 如果当前时间更接近下班时间，则为下班打卡
                                            check_type = "check_out"
                                            if current_minutes < check_out_minutes:
                                                status = "early_leave"

                                        # 设置成功消息
                                        best_match_info["message"] = f"{'上班' if check_type == 'check_in' else '下班'}打卡成功（时间检测已关闭）"
                                    else:
                                        # 正常的时间检测逻辑
                                        # 判断是否在允许的打卡时间范围内
                                        # 上班打卡时间范围：上班时间前1小时到上班时间后迟到阈值
                                        is_check_in_time = check_in_minutes - 60 <= current_minutes <= check_in_minutes + late_threshold
                                        # 下班打卡时间范围：下班时间前早退阈值到下班时间后1小时
                                        is_check_out_time = check_out_minutes - early_leave_threshold <= current_minutes <= check_out_minutes + 60

                                        # 如果不是工作日或不在允许的打卡时间范围内
                                        if not is_work_day or (not is_check_in_time and not is_check_out_time):
                                            # 设置标志变量，不插入新的考勤记录
                                            should_insert_record = False
                                            best_match_info["status"] = "not_check_time"
                                            best_match_info["message"] = "非打卡时间，请在规定时间内打卡"
                                            best_match_info["check_type"] = "unknown"
                                        else:
                                            # 判断打卡类型和状态
                                            if is_check_in_time:
                                                check_type = "check_in"
                                                # 如果超过上班时间，则为迟到
                                                if current_minutes > check_in_minutes:
                                                    status = "late"
                                            elif is_check_out_time:
                                                check_type = "check_out"
                                                # 如果早于下班时间，则为早退
                                                if current_minutes < check_out_minutes:
                                                    status = "early_leave"

                                    # 如果有最近的打卡记录，判断是否重复打卡
                                    if recent_record:
                                        recent_check_type = recent_record[2]
                                        # 如果最近的打卡类型与当前相同，则为重复打卡
                                        if recent_check_type == check_type:
                                            # 更新最佳匹配信息，添加重复打卡提示
                                            best_match_info["status"] = "duplicate"
                                            best_match_info["message"] = f"重复{'上班' if check_type == 'check_in' else '下班'}打卡，请勿频繁操作"
                                            best_match_info["check_type"] = check_type
                                            # 设置标志变量，不插入新的考勤记录
                                            should_insert_record = False

                                    # 只有当不是重复打卡时，才插入新的考勤记录
                                    if should_insert_record:
                                        # 插入考勤记录
                                        c.execute("""
                                            INSERT INTO attendance
                                            (person_id, timestamp, check_type, status, type)
                                            VALUES (?, datetime('now', 'localtime'), ?, ?, 'auto')
                                        """, (person_id, check_type, status))
                                        db_conn.commit()

                                    # 更新最佳匹配信息，添加打卡类型和状态
                                    best_match_info["check_type"] = check_type
                                    if "status" not in best_match_info:  # 只有当还没有设置 status 时才设置
                                        best_match_info["status"] = status

                                except Exception as e:
                                    print(f"Error in attendance processing: {type(e).__name__}: {str(e)}")
                                    best_match_info["check_type"] = check_type
                                    best_match_info["status"] = "error"
                                    best_match_info["message"] = f"处理考勤记录时出错: {type(e).__name__}"
                                    if db_conn:
                                        db_conn.rollback()
                            db_conn.close()
                        else:
                            best_match = None
                    else:
                        print(f"Column {distance_col} not found in DataFrame")
                        best_match = None

                if best_match_info:
                    await websocket.send_text(json.dumps(best_match_info))
                else:
                    await websocket.send_text(json.dumps({"status": "not_recognized"}))

            except Exception as find_err:
                print(f"Recognition error: {type(find_err).__name__}: {str(find_err)}")
                await websocket.send_text(json.dumps({"status": "error", "message": f"识别时发生错误: {type(find_err).__name__}"}))
    except Exception as e:
        # 捕获并记录WebSocket连接错误
        print(f"WebSocket error: {type(e).__name__}: {str(e)}")
    finally:
        if db_conn:
            try:
                db_conn.close()
            except Exception as e:
                print(f"Error closing database connection: {type(e).__name__}: {str(e)}")

# 调试端点
@app.get("/debug")
async def debug_main(request: Request):
    return templates.TemplateResponse("debug.html", {"request": request, "current_tab": "persons"})

@app.get("/debug/users")
async def debug_users(request: Request):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, username FROM users")
    users = c.fetchall()
    conn.close()
    return templates.TemplateResponse("debug.html", {"request": request, "current_tab": "users", "users": users})

@app.get("/debug/persons")
async def debug_persons(request: Request):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT * FROM persons")
    persons = c.fetchall()
    conn.close()
    return templates.TemplateResponse("debug.html", {"request": request, "current_tab": "persons", "persons": persons})

@app.get("/debug/records")
async def debug_records(request: Request):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("""
        SELECT a.id, p.name, p.position, a.timestamp as time
        FROM attendance a
        JOIN persons p ON a.person_id = p.id
        ORDER BY a.timestamp DESC
    """)
    records = c.fetchall()
    conn.close()
    return templates.TemplateResponse("debug.html", {"request": request, "current_tab": "records", "records": records})

# 删除API接口
@app.delete("/api/delete/user/{user_id}")
async def delete_user(user_id: int):
    try:
        # 不允许删除最后一个管理员用户
        conn = sqlite3.connect(DATABASE)
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM users")
        user_count = c.fetchone()[0]

        if user_count <= 1:
            conn.close()
            return {"success": False, "message": "不能删除最后一个管理员用户"}

        c.execute("DELETE FROM users WHERE id = ?", (user_id,))
        conn.commit()
        conn.close()
        return {"success": True, "message": f"用户 ID {user_id} 已成功删除"}
    except Exception as e:
        return {"success": False, "message": f"删除用户时出错: {str(e)}"}

@app.delete("/api/delete/person/{person_id}")
async def delete_person(person_id: int):
    try:
        conn = sqlite3.connect(DATABASE)
        c = conn.cursor()

        # 获取人脸图片路径
        c.execute("SELECT face_image_path FROM persons WHERE id = ?", (person_id,))
        result = c.fetchone()
        if not result:
            conn.close()
            return {"success": False, "message": f"未找到 ID 为 {person_id} 的人员"}

        face_image_path = result[0]

        # 删除相关的考勤记录
        c.execute("DELETE FROM attendance WHERE person_id = ?", (person_id,))

        # 删除人员记录
        c.execute("DELETE FROM persons WHERE id = ?", (person_id,))

        conn.commit()
        conn.close()

        # 删除人脸图片文件
        if face_image_path and os.path.exists(face_image_path):
            try:
                os.remove(face_image_path)
            except OSError as e:
                print(f"删除文件时出错: {e}")

        return {"success": True, "message": f"人员 ID {person_id} 及相关记录已成功删除"}
    except Exception as e:
        return {"success": False, "message": f"删除人员时出错: {str(e)}"}

@app.delete("/api/delete/record/{record_id}")
async def delete_record(record_id: int):
    try:
        conn = sqlite3.connect(DATABASE)
        c = conn.cursor()
        c.execute("DELETE FROM attendance WHERE id = ?", (record_id,))
        conn.commit()
        conn.close()
        return {"success": True, "message": f"考勤记录 ID {record_id} 已成功删除"}
    except Exception as e:
        return {"success": False, "message": f"删除考勤记录时出错: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)